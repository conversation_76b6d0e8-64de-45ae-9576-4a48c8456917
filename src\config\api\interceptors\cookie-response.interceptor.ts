import { createCookie } from "@/shared/lib/cookies/crud/create";
import { removeCookie } from "@/shared/lib/cookies/crud/remove";
import { AxiosInstance, AxiosResponse } from "axios";

interface CookieOptions {
	maxAge: number;
	expires?: Date;
	path?: string;
	secure?: boolean;
	sameSite?: "strict" | "lax" | "none";
	httpOnly?: boolean;
}

interface ParsedCookie {
	name: string;
	value: string;
	shouldRemove: boolean;
	options: CookieOptions;
}

export const cookieResponseInterceptor = (instance: AxiosInstance): void => {
	instance.interceptors.response.use(
		async (response: AxiosResponse) => {
			try {
				await processCookiesFromResponse(response);
			} catch (error) {
				console.error("Erro ao processar cookies da resposta:", error);
			}
			return response;
		},
		async error => {
			if (error.response) {
				try {
					await processCookiesFromResponse(error.response);
				} catch (cookieError) {
					console.error("Erro ao processar cookies da resposta de erro:", cookieError);
				}
			}
			return Promise.reject(error);
		}
	);
};

async function processCookiesFromResponse(response: AxiosResponse): Promise<void> {
	const setCookieHeaders = response.headers["set-cookie"];
	if (!setCookieHeaders || !Array.isArray(setCookieHeaders)) return;
	await Promise.all(
		setCookieHeaders.map(header =>
			processSingleCookie(header).catch(error => console.error("Erro ao processar cookie individual:", header, error))
		)
	);
}

async function processSingleCookie(cookieHeader: string): Promise<void> {
	const cookieData = parseCookieHeader(cookieHeader);
	if (!cookieData) return;
	const { name, value, shouldRemove, options } = cookieData;
	if (shouldRemove) {
		await removeCookie({ name });
		return;
	}
	await createCookie({ name, value, options });
}

function parseCookieHeader(cookieHeader: string): ParsedCookie | null {
	try {
		const [nameValuePair, ...directives] = cookieHeader.split(";").map(part => part.trim());
		const [name, value = ""] = nameValuePair.split("=");

		if (!name) return null;

		const options: CookieOptions = {
			maxAge: 86400,
		};

		const shouldRemove = isCookieToBeRemoved(value, directives);
		parseCookieDirectives(directives, options);

		return {
			name: name.trim(),
			value: value.trim(),
			shouldRemove,
			options,
		};
	} catch (error) {
		console.error("Erro ao fazer parse do cookie header:", cookieHeader, error);
		return null;
	}
}

function isCookieToBeRemoved(value: string, directives: string[]): boolean {
	if (value === "" || value === "deleted") return true;

	const maxAgeDirective = directives.find(d => d.toLowerCase().startsWith("max-age="));
	if (maxAgeDirective) {
		const maxAge = parseInt(maxAgeDirective.split("=")[1], 10);
		if (maxAge <= 0) return true;
	}

	const expiresDirective = directives.find(d => d.toLowerCase().startsWith("expires="));
	if (expiresDirective) {
		const expires = new Date(expiresDirective.split("=")[1]);
		if (expires.getTime() <= Date.now()) return true;
	}

	return false;
}

function parseCookieDirectives(directives: string[], options: CookieOptions): void {
	directives.forEach(directive => {
		const [key, value] = directive.split("=");
		const lowerKey = key.toLowerCase();

		switch (lowerKey) {
			case "max-age":
				options.maxAge = parseInt(value, 10);
				break;
			case "expires":
				options.expires = new Date(value);
				break;
			case "path":
				options.path = value;
				break;
			case "secure":
				options.secure = true;
				break;
			case "httponly":
				options.httpOnly = true;
				break;
			case "samesite":
				if (value && ["strict", "lax", "none"].includes(value.toLowerCase())) {
					options.sameSite = value.toLowerCase() as "strict" | "lax" | "none";
				}
				break;
		}
	});
}
